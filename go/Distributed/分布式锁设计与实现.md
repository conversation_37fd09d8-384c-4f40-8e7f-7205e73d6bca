# 分布式锁设计与实现

## 基本概念

### 1. 什么是分布式锁
在分布式系统中，用于控制多个节点对共享资源的并发访问，确保同一时间只有一个节点可以操作资源。

### 2. 应用场景
- **防止重复执行**：定时任务、消息消费
- **资源互斥访问**：库存扣减、账户余额
- **数据一致性**：缓存更新、配置修改

## 实现方案

### 1. 基于Redis的分布式锁

#### 基本实现
- **加锁**：`SET key value NX PX expire_time`
- **解锁**：Lua脚本保证原子性
- **优点**：性能高、实现简单
- **缺点**：单点故障风险

#### Redlock算法
- **原理**：在多个Redis实例上获取锁
- **要求**：大多数实例(N/2+1)获取成功
- **步骤**：
  1. 记录开始时间
  2. 依次向所有实例申请锁
  3. 计算获取锁的总时间
  4. 判断是否获取成功

### 2. 基于ZooKeeper的分布式锁

#### 实现原理
- **临时顺序节点**：创建临时顺序节点
- **最小节点获锁**：序号最小的节点获得锁
- **监听机制**：监听前一个节点的删除事件
- **优点**：强一致性、公平锁
- **缺点**：性能相对较低

#### 实现步骤
1. 创建临时顺序节点
2. 获取所有子节点并排序
3. 判断是否为最小节点
4. 不是则监听前一个节点

### 3. 基于数据库的分布式锁

#### 实现方式
- **唯一约束**：利用数据库唯一索引
- **乐观锁**：版本号机制
- **悲观锁**：SELECT FOR UPDATE
- **优点**：强一致性、事务支持
- **缺点**：性能较差、死锁风险

## 关键问题解决

### 1. 死锁预防
- **超时机制**：设置锁的过期时间
- **心跳续约**：定期延长锁的有效期
- **异常处理**：进程崩溃时自动释放锁

### 2. 锁的安全性
- **唯一标识**：使用UUID标识锁的持有者
- **原子操作**：加锁和解锁的原子性
- **误删防护**：验证锁的持有者身份

### 3. 性能优化
- **锁粒度**：细化锁的范围
- **本地缓存**：减少分布式锁的使用
- **异步处理**：非关键路径异步执行

## 各方案对比

### 性能对比
- **Redis**：性能最高，毫秒级响应
- **ZooKeeper**：性能中等，网络开销较大
- **数据库**：性能最低，受限于数据库性能

### 可靠性对比
- **Redis**：单点故障风险，Redlock提高可靠性
- **ZooKeeper**：强一致性，集群容错
- **数据库**：事务保证，但可能死锁

### 复杂度对比
- **Redis**：实现简单，但需考虑边界情况
- **ZooKeeper**：实现复杂，但框架支持好
- **数据库**：实现简单，但性能限制

## 最佳实践

### 1. 选择原则
- **高性能场景**：选择Redis
- **强一致性场景**：选择ZooKeeper
- **简单场景**：选择数据库

### 2. 设计要点
- **合理的超时时间**：避免死锁和误释放
- **重试机制**：获取锁失败时的重试策略
- **监控告警**：锁的使用情况和异常监控

### 3. 常见陷阱
- **时钟漂移**：不同机器时钟不同步
- **网络分区**：网络故障导致的锁失效
- **GC停顿**：长时间GC导致锁超时

## Go语言实现示例

### Redis分布式锁
```go
type RedisLock struct {
    client *redis.Client
    key    string
    value  string
    expire time.Duration
}

func (l *RedisLock) Lock() bool {
    return l.client.SetNX(l.key, l.value, l.expire).Val()
}

func (l *RedisLock) Unlock() {
    script := `
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
    `
    l.client.Eval(script, []string{l.key}, l.value)
}
```

## 面试要点

### 1. 基础问题
- **分布式锁的作用？** 控制分布式环境下的资源访问
- **实现方案有哪些？** Redis、ZooKeeper、数据库
- **如何防止死锁？** 超时机制、心跳续约

### 2. 深入问题
- **Redlock算法原理？** 多实例获取锁，大多数成功
- **ZooKeeper如何实现公平锁？** 临时顺序节点
- **如何保证锁的安全性？** 唯一标识、原子操作

### 3. 实际应用
- **性能优化策略？** 减少锁粒度、本地缓存
- **如何选择方案？** 根据性能、一致性要求选择
- **常见问题及解决？** 时钟漂移、网络分区、GC停顿
