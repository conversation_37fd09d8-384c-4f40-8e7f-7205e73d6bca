# CAP理论

## 核心概念
CAP理论由Eric Brewer在2000年提出，指出分布式系统无法同时满足一致性、可用性和分区容忍性三个特性。

## 三大特性

### 1. 一致性（Consistency）
- **定义**：所有节点同时看到相同数据
- **要求**：任何读操作返回最新写操作结果
- **代价**：可能影响性能和可用性

### 2. 可用性（Availability）
- **定义**：系统持续提供服务，即使部分节点失效
- **要求**：任何时候都能处理请求并返回结果
- **代价**：可能返回过时数据

### 3. 分区容忍性（Partition Tolerance）
- **定义**：网络分区时系统继续运行
- **要求**：节点间通信中断时仍能提供服务
- **必要性**：分布式系统必须具备的特性

---

## 三选二原则

### 1. CP（一致性 + 分区容忍性）
- **特点**：保证数据一致性，牺牲可用性
- **应用**：ZooKeeper、HBase、MongoDB
- **场景**：金融系统、配置中心

### 2. AP（可用性 + 分区容忍性）
- **特点**：保证高可用性，牺牲强一致性
- **应用**：Cassandra、DynamoDB、CouchDB
- **场景**：社交媒体、内容分发

### 3. CA（一致性 + 可用性）
- **特点**：理论上不可能，网络分区不可避免
- **现实**：单机系统或局域网系统

## BASE理论

### 基本概念
- **Basically Available**：基本可用
- **Soft State**：软状态，允许数据暂时不一致
- **Eventually Consistent**：最终一致性

### 与CAP关系
BASE理论是对CAP理论AP选择的具体实现指导

## 实际应用选择

### 选择CP的场景
- **金融交易**：数据一致性至关重要
- **配置管理**：配置信息必须一致
- **分布式锁**：互斥性要求强一致

### 选择AP的场景
- **社交网络**：用户体验优先
- **内容分发**：可用性比一致性重要
- **日志收集**：允许短暂数据丢失

## 面试要点
1. **CAP无法同时满足**：网络分区不可避免
2. **实际选择**：通常在CP和AP之间选择
3. **BASE理论**：AP选择的实现指导
4. **业务驱动**：根据业务需求选择合适策略