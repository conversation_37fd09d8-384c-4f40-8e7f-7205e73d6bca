# Goroutine阻塞场景

## 1. I/O操作阻塞
- **网络请求**：HTTP请求、TCP连接等
- **文件操作**：读写文件、目录操作等
- **数据库操作**：SQL查询、事务处理等

## 2. Channel操作阻塞
- **无缓冲channel**：发送方等待接收方，接收方等待发送方
- **有缓冲channel**：缓冲区满时发送阻塞，缓冲区空时接收阻塞
- **关闭的channel**：向已关闭channel发送数据会panic

## 3. 锁竞争阻塞
- **Mutex**：互斥锁竞争导致阻塞
- **RWMutex**：读写锁竞争导致阻塞
- **死锁**：多个goroutine相互等待锁

## 4. 条件变量阻塞
- **sync.Cond**：等待条件满足时阻塞
- **Wait()**：释放锁并等待通知
- **Signal/Broadcast**：唤醒等待的goroutine

## 5. Select语句阻塞
- **所有case阻塞**：所有channel操作都无法进行
- **无default分支**：没有默认处理分支时阻塞
- **超时控制**：使用time.After避免永久阻塞

## 6. 时间相关阻塞
- **time.Sleep()**：主动休眠指定时间
- **time.After()**：等待定时器触发
- **Ticker**：等待周期性定时器

## 7. 系统调用阻塞
- **系统调用**：文件操作、网络操作等系统调用
- **CGO调用**：调用C语言库函数
- **外部依赖**：等待外部服务响应

## 8. 程序生命周期
- **主goroutine退出**：main函数结束导致所有goroutine终止
- **等待子goroutine**：使用sync.WaitGroup等待
- **优雅关闭**：处理信号量实现优雅退出

## 避免阻塞的策略
1. **使用缓冲channel**：减少同步等待
2. **设置超时**：使用context或time.After
3. **非阻塞操作**：使用select的default分支
4. **合理设计锁**：减少锁的持有时间
5. **异步处理**：使用goroutine异步处理耗时操作